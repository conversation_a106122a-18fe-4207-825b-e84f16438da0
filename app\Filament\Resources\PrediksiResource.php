<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PrediksiResource\Pages;
use App\Models\{BahanBaku, Prediksi, Stok};
use Carbon\Carbon;
use Filament\Forms\{Form, Components};
use Filament\Resources\Resource;
use Filament\Tables\{Table, Actions, Columns};

class PrediksiResource extends Resource
{
    protected static ?string $slug = 'prediksi';
    public static function getSlug(): string { return 'prediksi'; }
    protected static ?string $model = Prediksi::class;
    protected static ?string $modelLabel = 'Prediksi';
    protected static ?string $pluralModelLabel = 'Prediksi';
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Components\Section::make('Prediksi Weighted Moving Average')
                    ->schema([
                        Components\Select::make('bahan_baku_id')
                            ->label('Bahan Baku')
                            ->options(BahanBaku::pluck('nama', 'id'))
                            ->required()
                            ->native(false)
                            ->live(),
                        Components\Actions::make([
                            Components\Actions\Action::make('cetak')
                                ->label('Cetak')
                                ->icon('heroicon-o-printer')
                                ->color('success')
                                ->url(fn ($get) => $get('bahan_baku_id') ? route('prediksi.preview.pdf', ['bahanBakuId' => $get('bahan_baku_id')]) : null)
                                ->openUrlInNewTab()
                                ->visible(fn ($get) => $get('bahan_baku_id') !== null),
                        ]),
                    ])
                    ->columns(1),

                Components\Section::make('Hasil Prediksi')
                    ->schema([
                        Components\Placeholder::make('')
                            ->content(function ($get) {
                                $bahanBakuId = $get('bahan_baku_id');

                                if (!$bahanBakuId) {
                                    return 'Silakan pilih bahan baku untuk melihat prediksi.';
                                }

                                $result = Prediksi::calculatePrediction($bahanBakuId);

                                if (!$result['success']) {
                                    return $result['message'];
                                }

                                $predictions = $result['data']['predictions'];
                                $finalPrediction = $result['data']['prediction'];
                                $weights = $result['data']['weights'];
                                $nextPeriode = $result['data']['periode'];

                                // Build the results table HTML
                                $table = '<div class="space-y-4">';

                                // Updated table HTML with responsive design and Alpine.js store
                                $table = '<div class="space-y-4 overflow-x-auto" x-data="{ activeDropdown: null }">';

                                $table .= '<table class="w-full border-collapse border border-gray-300 dark:border-gray-700 min-w-full table-auto">
                                    <thead class="bg-gray-100 dark:bg-gray-800">
                                        <tr>
                                            <th class="border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100 whitespace-nowrap">Periode</th>
                                            <th class="border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100 whitespace-nowrap">Aktual</th>
                                            <th class="border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100 whitespace-nowrap">Prediksi</th>
                                            <th class="border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100 whitespace-nowrap">Eror</th>
                                            <th class="border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100 whitespace-nowrap">MAPE</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-900">';

                                // Reverse the predictions array to show newest at the bottom
                                $predictions = array_reverse($predictions);
                                $historicalData = $result['data']['historicalData'];
                                $totalWeight = array_sum($weights);

                                foreach ($predictions as $index => $pred) {
                                    // Calculate details for dropdowns
                                    $predictionDetails = '';
                                    if ($pred['prediksi'] !== null && isset($pred['calculation_data'])) {
                                        $steps = '';
                                        // Get current weights and total for display
                                        $currentWeights = $result['data']['weights'];
                                        $currentTotalWeight = array_sum($currentWeights);
                                        foreach ($pred['calculation_data'] as $step) {
                                            // $step['weight'] already contains the specific weight used for that row's calculation step
                                            $steps .= "<p>{$step['month']}: {$step['value']} × {$step['weight']}/{$currentTotalWeight} = " .
                                                     number_format($step['weighted_value'], 2) . "</p>";
                                        }

                                        $predictionDetails = "<div class='text-sm p-4 bg-gray-50 dark:bg-gray-800 rounded'>
                                            <p class='font-semibold mb-2'>Perhitungan WMA:</p>
                                            <div class='space-y-2'>
                                                {$steps}
                                                <p class='font-semibold'>= " . number_format((float)$pred['prediksi'], 1) . "</p>
                                            </div>
                                        </div>";
                                    }

                                    // Error details dropdown
                                    $errorDetails = '';
                                    if ($pred['error'] !== null) {
                                        $errorDetails = "<div class='text-sm p-4 bg-gray-50 dark:bg-gray-800 rounded'>
                                            <p class='font-semibold mb-2'>Perhitungan Error:</p>
                                            <div class='space-y-2'>
                                                <p>Error = Aktual - Prediksi</p>
                                                <p>= {$pred['aktual']} - " . number_format((float)$pred['prediksi'], 1) . "</p>
                                                <p class='font-semibold'>= " . number_format((float)$pred['error'], 2) . "</p>
                                            </div>
                                        </div>";
                                    }

                                    // MAPE details dropdown
                                    $mapeDetails = '';
                                    if ($pred['mape'] !== null) {
                                        $mapeDetails = "<div class='text-sm p-4 bg-gray-50 dark:bg-gray-800 rounded'>
                                            <p class='font-semibold mb-2'>Perhitungan MAPE:</p>
                                            <div class='space-y-2'>
                                                <p>MAPE = |Error ÷ Aktual| × 100%</p>
                                                <p>= |" . number_format((float)$pred['error'], 2) . " ÷ {$pred['aktual']}| × 100%</p>
                                                <p class='font-semibold'>= " . number_format((float)$pred['mape'], 2) . "%</p>
                                            </div>
                                        </div>";
                                    }

                                    // Table cells with dropdown styling
                                    $table .= "<tr>
                                        <td class='border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100'>{$pred['periode']->translatedFormat('F Y')}</td>
                                        <td class='border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100'>" . ((float)$pred['aktual'] == (int)$pred['aktual'] ? number_format((float)$pred['aktual'], 0) : number_format((float)$pred['aktual'], 1)) . " {$pred['satuan']}</td>
                                        <td class='border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100'>
                                            <div x-data=\"{ dropdownId: 'pred-{$index}' }\" class=\"relative\">
                                                <div @click.prevent.stop=\"
                                                    if (activeDropdown === dropdownId) {
                                                        activeDropdown = null;
                                                    } else {
                                                        activeDropdown = dropdownId;
                                                    }
                                                \"
                                                class=\"cursor-pointer flex items-center justify-between\">
                                                    " . ($pred['prediksi'] === null ? '-' : ((float)$pred['prediksi'] == (int)$pred['prediksi'] ? number_format((float)$pred['prediksi'], 0) : number_format((float)$pred['prediksi'], 1)) . " {$pred['satuan']}") . "
                                                    " . ($pred['prediksi'] !== null ? '<button type="button" class="ml-2 text-gray-400 hover:text-gray-600">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                                        </svg>
                                                    </button>' : '') . "
                                                </div>
                                                " . ($predictionDetails ? "<div x-show=\"activeDropdown === dropdownId\"
                                                    @click.outside=\"activeDropdown = null\"
                                                    class=\"absolute z-50 w-96 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 max-h-96 overflow-y-auto top-full left-0 mt-1\"
                                                    style=\"max-width: calc(100vw - 2rem);\">
                                                    {$predictionDetails}
                                                </div>" : '') . "
                                            </div>
                                        </td>
                                        <td class='border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100'>
                                            <div x-data=\"{ dropdownId: 'error-{$index}' }\" class=\"relative\">
                                                <div @click.prevent.stop=\"
                                                    if (activeDropdown === dropdownId) {
                                                        activeDropdown = null;
                                                    } else {
                                                        activeDropdown = dropdownId;
                                                    }
                                                \"
                                                class=\"cursor-pointer flex items-center justify-between\">
                                                    " . ($pred['error'] === null ? '-' : number_format($pred['error'], 2)) . "
                                                    " . ($pred['error'] !== null ? '<button type="button" class="ml-2 text-gray-400 hover:text-gray-600">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                                        </svg>
                                                    </button>' : '') . "
                                                </div>
                                                " . ($errorDetails ? "<div x-show=\"activeDropdown === dropdownId\"
                                                    @click.outside=\"activeDropdown = null\"
                                                    class=\"absolute z-50 w-96 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 max-h-96 overflow-y-auto top-full left-0 mt-1\"
                                                    style=\"max-width: calc(100vw - 2rem);\">
                                                    {$errorDetails}
                                                </div>" : '') . "
                                            </div>
                                        </td>
                                        <td class='border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100'>
                                            <div x-data=\"{ dropdownId: 'mape-{$index}' }\" class=\"relative\">
                                                <div @click.prevent.stop=\"
                                                    if (activeDropdown === dropdownId) {
                                                        activeDropdown = null;
                                                    } else {
                                                        activeDropdown = dropdownId;
                                                    }
                                                \"
                                                class=\"cursor-pointer flex items-center justify-between\">
                                                    " . ($pred['mape'] === null ? '-' : number_format($pred['mape'], 2) . '%') . "
                                                    " . ($pred['mape'] !== null ? '<button type="button" class="ml-2 text-gray-400 hover:text-gray-600">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                                        </svg>
                                                    </button>' : '') . "
                                                </div>
                                                " . ($mapeDetails ? "<div x-show=\"activeDropdown === dropdownId\"
                                                    @click.outside=\"activeDropdown = null\"
                                                    class=\"absolute z-50 w-96 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 max-h-96 overflow-y-auto top-full left-0 mt-1\"
                                                    style=\"max-width: calc(100vw - 2rem);\">
                                                    {$mapeDetails}
                                                </div>" : '') . "
                                            </div>
                                        </td>
                                    </tr>";
                                }

                                $table .= "<tr class='bg-gray-50 dark:bg-gray-800'>
                                    <td colspan='4' class='border border-gray-300 dark:border-gray-700 p-2 text-center font-semibold text-gray-900 dark:text-gray-100'>MAPE (Mean Absolute Percentage Error)</td>
                                    <td class='border border-gray-300 dark:border-gray-700 p-2 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100'>
                                        <div x-data=\"{ dropdownId: 'mape-summary' }\" class=\"relative\">
                                            <div @click.prevent.stop=\"
                                                if (activeDropdown === dropdownId) {
                                                    activeDropdown = null;
                                                } else {
                                                    activeDropdown = dropdownId;
                                                }
                                            \"
                                            class=\"cursor-pointer flex items-center justify-between\">
                                                " . number_format($result['data']['mape'], 2) . "%
                                                <button type=\"button\" class=\"ml-2 text-gray-400 hover:text-gray-600\">
                                                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"/>
                                                    </svg>
                                                </button>
                                            </div>
                                            <div x-show=\"activeDropdown === dropdownId\"
                                                @click.outside=\"activeDropdown = null\"
                                                class=\"absolute z-50 w-96 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 max-h-96 overflow-y-auto top-full left-0 mt-1\"
                                                style=\"max-width: calc(100vw - 2rem);\">
                                                <div class='text-sm p-4 bg-gray-50 dark:bg-gray-800 rounded'>
                                                    <p class='font-semibold mb-2'>Perhitungan MAPE:</p>
                                                    <div class='space-y-2'>
                                                        <p>MAPE = (Σ |Eror ÷ Aktual| × 100%) ÷ n</p>
                                                        <p>= (" . implode(' + ', array_map(function($pred) {
                                                            return $pred['mape'] !== null ? number_format($pred['mape'], 2) : '0';
                                                        }, array_filter($predictions, function($pred) {
                                                            return $pred['mape'] !== null;
                                                        }))) . ") ÷ " . count(array_filter($predictions, function($pred) {
                                                            return $pred['mape'] !== null;
                                                        })) . "</p>
                                                        <p class='font-semibold'>= " . number_format((float)$result['data']['mape'], 2) . "%</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>";

                                $finalCalculationSteps = $result['data']['final_calculation'];
                                $finalPredictionDisplayDetails = '';
                                if ($finalPrediction !== null && !empty($finalCalculationSteps)) {
                                    $steps = '';
                                    $currentWeights = $result['data']['weights'];
                                    $currentTotalWeight = array_sum($currentWeights);
                                    foreach ($finalCalculationSteps as $step) {
                                        $steps .= "<p>{$step['month']}: {$step['value']} × {$step['weight']}/{$currentTotalWeight} = " .
                                                 number_format($step['weighted_value'], 2) . "</p>";
                                    }
                                    $finalPredictionDisplayDetails = "<div class='text-sm p-4 bg-gray-50 dark:bg-gray-800 rounded'>
                                        <p class='font-semibold mb-2'>Perhitungan WMA:</p>
                                        <div class='space-y-2'>
                                            {$steps}
                                            <p class='font-semibold'>= " . number_format($finalPrediction, 1) . "</p>
                                        </div>
                                    </div>";
                                }

                                $table .= "<tr class='bg-white dark:bg-gray-900'>
                                    <td class='border border-gray-300 dark:border-gray-700 p-2 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-gray-900 dark:text-gray-100'>Hasil Prediksi</td>
                                    <td class='border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100'>{$nextPeriode->translatedFormat('F Y')}</td>
                                    <td colspan='3' class='border border-gray-300 dark:border-gray-700 p-2 text-gray-900 dark:text-gray-100'>
                                        <div x-data=\"{ dropdownId: 'final-pred' }\" class=\"relative\">
                                            <div @click.prevent.stop=\"if (activeDropdown === dropdownId) { activeDropdown = null; } else { activeDropdown = dropdownId; }\" class=\"cursor-pointer flex items-center justify-between\">
                                                " . ((float)$finalPrediction == (int)$finalPrediction ? number_format((float)$finalPrediction, 0) : number_format((float)$finalPrediction, 1)) . " {$result['data']['satuan']}
                                                <button type=\"button\" class=\"ml-2 text-gray-400 hover:text-gray-600\"><svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"/></svg></button>
                                            </div>
                                            <div x-show=\"activeDropdown === dropdownId\"
                                                @click.outside=\"activeDropdown = null\"
                                                class=\"absolute z-50 w-96 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 max-h-96 overflow-y-auto top-full left-0 mt-1\"
                                                style=\"max-width: calc(100vw - 2rem);\">
                                                {$finalPredictionDisplayDetails}
                                            </div>
                                        </div>
                                    </td>
                                </tr>";

                                $table .= '</tbody></table>';

                                $table .= '</div>';

                                return new \Illuminate\Support\HtmlString($table);
                            })
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Columns\TextColumn::make('periode')
                    ->label('Periode')
                    ->date('F Y')
                    ->sortable()
                    ->searchable(query: function (\Illuminate\Database\Eloquent\Builder $query, string $search): \Illuminate\Database\Eloquent\Builder {
                    return $query->where(function (\Illuminate\Database\Eloquent\Builder $subQuery) use ($search) {
                        // Original English month/year search
                        $subQuery->orWhereRaw("DATE_FORMAT(periode, '%M %Y') LIKE ?", ["%{$search}%"])
                                 ->orWhereRaw("DATE_FORMAT(periode, '%Y') LIKE ?", ["%{$search}%"]);

                        // Indonesian month search logic
                        $indonesianMonths = [
                            'januari' => 1, 'februari' => 2, 'maret' => 3, 'april' => 4,
                            'mei' => 5, 'juni' => 6, 'juli' => 7, 'agustus' => 8,
                            'september' => 9, 'oktober' => 10, 'november' => 11, 'desember' => 12,
                        ];
                        $searchLower = strtolower(trim($search));
                        $parts = preg_split('/\s+/', $searchLower, -1, PREG_SPLIT_NO_EMPTY);

                        $monthNumber = null;
                        $yearNumber = null;

                        if (count($parts) === 1) {
                            if (is_numeric($parts[0]) && strlen($parts[0]) === 4) {
                                $yearNumber = $parts[0];
                            } else {
                                $monthNumber = $indonesianMonths[$parts[0]] ?? null;
                            }
                        } elseif (count($parts) === 2) {
                            // Try "Month Year"
                            if (isset($indonesianMonths[$parts[0]]) && is_numeric($parts[1]) && strlen($parts[1]) === 4) {
                                $monthNumber = $indonesianMonths[$parts[0]];
                                $yearNumber = $parts[1];
                            }
                            // Try "Year Month"
                            elseif (is_numeric($parts[0]) && strlen($parts[0]) === 4 && isset($indonesianMonths[$parts[1]])) {
                                $yearNumber = $parts[0];
                                $monthNumber = $indonesianMonths[$parts[1]];
                            }
                        }

                        if ($monthNumber && $yearNumber) {
                            $subQuery->orWhere(function (\Illuminate\Database\Eloquent\Builder $q) use ($monthNumber, $yearNumber) {
                                $q->whereMonth('periode', $monthNumber)
                                  ->whereYear('periode', $yearNumber);
                            });
                        } elseif ($monthNumber) {
                            $subQuery->orWhereMonth('periode', $monthNumber);
                        } elseif ($yearNumber) {
                            $subQuery->orWhereYear('periode', $yearNumber);
                        }
                    });
                }),
                Columns\TextColumn::make('bahanBaku.nama')
                    ->label('Bahan Baku')
                    ->searchable()
                    ->sortable(),
                Columns\TextColumn::make('hasil_prediksi')
                    ->label('Prediksi')
                    ->formatStateUsing(function ($state, $record) {
                        $latestStock = Stok::where('bahan_baku_id', $record->bahan_baku_id)
                            ->latest('periode')
                            ->first();

                        $formattedState = $state;
                        if (is_numeric($state)) {
                            // Check if the number is an integer or has .0 decimal
                            if (fmod((float)$state, 1) == 0) {
                                $formattedState = number_format((float)$state, 0);
                            } else {
                                $formattedState = number_format((float)$state, 1);
                            }
                        }

                        return $latestStock ? "{$formattedState} {$latestStock->satuan}" : $formattedState;
                    }),
                Columns\TextColumn::make('mape')
                    ->label('MAPE')
                    ->formatStateUsing(fn (string $state): string => number_format($state, 2) . '%'),
            ])
            ->defaultSort('periode', 'desc')
            ->actions([
                Actions\Action::make('cetak')
                    ->label('Cetak')
                    ->icon('heroicon-o-printer')
                    ->url(fn (Prediksi $record): string => route('prediksi.pdf', $record)) // Pass the Prediksi record for route model binding
                    ->openUrlInNewTab(),
                Actions\DeleteAction::make()
                    ->label('Hapus')
                    ->modalHeading('Hapus Data Prediksi')
                    ->modalDescription('Apakah Anda yakin ingin menghapus data prediksi ini?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->modalCancelActionLabel('Batal'),
            ])
            ->bulkActions([
                Actions\BulkActionGroup::make([
                    Actions\DeleteBulkAction::make()
                        ->label('Hapus yang dipilih')
                        ->modalHeading('Hapus Data Prediksi')
                        ->modalDescription('Apakah Anda yakin ingin menghapus data prediksi yang dipilih?')
                        ->modalSubmitActionLabel('Ya, Hapus')
                        ->modalCancelActionLabel('Batal'),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPrediksis::route('/'),
            'create' => Pages\CreatePrediksi::route('/create'),
        ];
    }
}